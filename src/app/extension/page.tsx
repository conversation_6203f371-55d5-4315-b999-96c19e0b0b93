"use client";

import { useEffect, useState, useCallback, useRef } from "react";
import {
  AlertCircle,
  Box,
  List,
  X,
  Lock,
  UserCircle,
  AlertTriangle,
  TrendingUp,
  Info,
  Eye,
  RefreshCw,
} from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { LoginModal } from "@/components/auth/LoginModal";
import {
  fetchProductData,
  mapApiResponseToProductData,
  extractAsinFromUrl,
  parseAmazonProductPage,
  fetchProfitCalculator,
  fetchGatedStatus,
} from "@/lib/api/productApi";
import CompactProfitCalculator from "@/components/ProfitCalculator";
import { ProductApiResponse } from "@/lib/api/productApi";
import PriceHistorySection from "@/components/charts/KeepaSection";
import BuyBoxHistorySection from "@/components/charts/BuyBoxHistorySection";
import AverageDataSection from "@/components/data/AverageDataSection";
import SettingsPanel from "@/components/settings/SettingsPanel";
import AISummarizer from "@/components/ai/AISummarizer";
import ChatBot from "@/components/ai/ChatBot";
import ClickbuyAds from "@/components/advert/ClickbuyAds";

import useLocalStorage from "@/hooks/useLocalStorage";
import { useRouter } from "next/navigation";

import Image from "next/image";
import {
  OfferChartItem,
  OfferItem,
  ProductData,
  ProfitData,
} from "@/lib/types/home";

const getCurrencySymbol = (country: string): string => {
  switch (country) {
    case "US":
      return "$";
    case "UK":
    case "GB":
      return "£";
    case "DE":
    case "FR":
    case "IT":
    case "ES":
      return "€";
    case "JP":
    case "CN":
      return "¥";
    case "CA":
      return "C$";
    case "IN":
      return "₹";
    case "MX":
      return "$";
    default:
      return "£";
  }
};

// Helper function to format numbers
const formatNumber = (value: number | undefined | null) => {
  if (value === undefined || value === null) return "";
  return value.toLocaleString();
};

export default function Home() {
  
  const [productInfo, setProductInfo] = useState<ProductData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [sectionLoading, setSectionLoading] = useState({
    basic: true,
    priceHistory: false,
    offers: false,
    calculator: false,
    gated: false,
  });
  const [error, setError] = useState<string | null>(null);
  const [sectionErrors, setSectionErrors] = useState({
    basic: null,
    priceHistory: null,
    offers: null,
    calculator: null,
    gated: null,
  });
  const [activeTab, setActiveTab] = useState("live");
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [apiData, setApiData] = useState<ProductApiResponse | null>(null);
  const [offerChartData, setOfferChartData] = useState<OfferChartItem[]>([]);
  const [profitData, setProfitData] = useState<ProfitData | null>(null);
  const [calculatorOpen, setCalculatorOpen] = useState(true); // Default to open
  const [pageUrl, setPageUrl] = useState<string | null>(null);
  const [hasSignaledReady, setHasSignaledReady] = useState(false);
  const [purchasePrice, setPurchasePrice] = useState<number>(0);
  const [isCalculating, setIsCalculating] = useState(false);
  const [isOffersExpanded, setIsOffersExpanded] = useState(false);
  const [gatedStatus, setGatedStatus] = useState<any>(null);
  const [isGatedLoading, setIsGatedLoading] = useState(false);
  const [vatPercentage, setVatPercentage] = useState<string>("20");
  const [isVatRegistered, setIsVatRegistered] = useState<boolean>(false);
  const [lastProfitDataId, setLastProfitDataId] = useState<string | null>(null);
  const [isGatingTooltipOpen, setIsGatingTooltipOpen] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const [buyBoxPrice, setBuyBoxPrice] = useState<number>(0);
  const [selectedCountry, setSelectedCountry] = useLocalStorage<string>(
    "selectedCountry",
    "GB",
  );
  const [recentLoginTimestamp, setRecentLoginTimestamp] = useState<
    number | null
  >(null);
  const [dataLoadingProgress, setDataLoadingProgress] = useState(0);
  const priceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const buyBoxTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [showSettings, setShowSettings] = useState(false);
  const [priceHistoryOpen, setPriceHistoryOpen] = useState<boolean>(true); // Default to open
  const [showSellMoreTooltip, setShowSellMoreTooltip] = useState(false);
  const [clickbuyAdsOpen, setClickbuyAdsOpen] = useState<boolean>(true); // Default to open
  const [showChatBot, setShowChatBot] = useState(false);
  const [warningsOpen, setWarningsOpen] = useState(true); // Add this line

  const [isPinned, setIsPinned] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const isTooltipOpen = isPinned || isHovered;

  const toggleClickbuyAds = (): void => {
    setClickbuyAdsOpen(!clickbuyAdsOpen);
  };

  const {
    user,
    isAuthenticated,
    isLoading: authLoading,
    error: authError,
    login,
    logout,
  } = useAuth();

  const [showVariationsTooltip, setShowVariationsTooltip] = useState(false);
  const [inputPurchasePrice, setInputPurchasePrice] = useState<string>("");
  const [inputBuyBoxPrice, setInputBuyBoxPrice] = useState<string>("");
  const router = useRouter();

  const LOGIN_GRACE_PERIOD = 3000;

  // Helper functions
  const updateSectionLoading = useCallback((section: string, isLoading: boolean) => {
    setSectionLoading(prev => ({ ...prev, [section]: isLoading }));
  }, []);

  const updateSectionError = useCallback((section: string, error: string | null) => {
    setSectionErrors(prev => ({ ...prev, [section]: error }));
  }, []);

  const formatToCurrency = useCallback((value: number | undefined | null, locale = selectedCountry) => {
    if (value === undefined || value === null) return "";
    try {
      const numValue = Number(value);
      if (isNaN(numValue)) return "";
      return `${getCurrencySymbol(locale)}${numValue.toFixed(2)}`;
    } catch (error) {
      console.error("Error formatting currency:", error);
      return "";
    }
  }, [selectedCountry]);

  // Core data fetching and processing
  const fetchEnhancedProductData = useCallback(async (asin: string) => {
    try {
      setIsLoading(true);
      updateSectionLoading("basic", true);
      
      const apiResponse = await fetchProductData(asin, selectedCountry);
      setDataLoadingProgress(50);
      
      setApiData(apiResponse);
      const enhancedData = mapApiResponseToProductData(apiResponse);
      
      setProductInfo(prevState => ({
        ...enhancedData,
        estimated_sales: apiResponse?.summary?.monthly_sold || prevState?.estimated_sales || 0,
        mainImage: prevState?.mainImage || enhancedData.mainImage,
        warnings: {
          amz_in_buy_box: false,
          adult_product: false,
          meltable: false,
          ...(prevState?.warnings || {}),
          ...(enhancedData.warnings || {}),
        },
        pricing: {
          non_vat_pricing: {
            buy_box_price: 0,
            fba_fee: 0,
            profit: 0,
            referral_fee: 0,
            roi: 0,
            sales_rank: 0,
            seller_price: 0,
            total_fee: 0,
            variable_closing_fee: 0,
            ...(prevState?.pricing?.non_vat_pricing || {}),
            ...(enhancedData.pricing?.non_vat_pricing || {}),
          },
        },
      }));

      setDataLoadingProgress(100);
      setIsLoading(false);
      updateSectionLoading("basic", false);
      updateSectionError("basic", null);
      
    } catch (err) {
      console.error("Error fetching enhanced product data:", err);
      if (isAuthenticated) {
        setError("Failed to fetch detailed product information. Please try again.");
      }
      setIsLoading(false);
      updateSectionLoading("basic", false);
    }
  }, [isAuthenticated, selectedCountry, updateSectionLoading, updateSectionError]);

  // Profit calculation
  const calculateProfit = useCallback(async (price: number, boxPrice?: number, newVat?: number) => {
    if (!apiData || !isAuthenticated) return;

    try {
      setIsCalculating(true);
      updateSectionLoading("calculator", true);

      const response = await fetchProfitCalculator({
        buy_box_price: boxPrice || buyBoxPrice || 0,
        fba_fees: productInfo?.pricing?.non_vat_pricing?.fba_fee || 0,
        referral_percent: apiData?.pricing?.referral_fee?.referral_fee_percentage || 15,
        seller_price: price || 0,
        variable_closing_fee: 0,
        sales_rank: productInfo?.pricing?.non_vat_pricing?.sales_rank || 0,
        country: selectedCountry,
        vat: newVat ?? parseFloat(vatPercentage),
      });

      if (response?.not_vat_registed?.fees) {
        setProfitData({
          not_vat_registed: response.not_vat_registed,
          buy_box_price: response.metrics.buybox_price,
          fba_fee: response.metrics.fba_fees,
          profit: response.not_vat_registed.fees.profit,
          referral_fee: response.metrics.referral_fee,
          roi: response.not_vat_registed.fees.roi,
          total_fee: response.not_vat_registed.fees.total_fees,
          variable_closing_fee: response.metrics.variable_closing_fee || 0,
          purchase_price: price,
        });
        setError(null);
        updateSectionError("calculator", null);
      }
    } catch (error) {
      console.error("Error calculating profit:", error);
    } finally {
      setIsCalculating(false);
      updateSectionLoading("calculator", false);
    }
  }, [apiData, isAuthenticated, buyBoxPrice, productInfo, selectedCountry, vatPercentage, updateSectionLoading, updateSectionError]);

  // Event handlers
  const handlePurchasePriceChange = useCallback((newValue: string) => {
    setInputPurchasePrice(newValue);
    if (priceTimeoutRef.current) clearTimeout(priceTimeoutRef.current);
    
    const numValue = parseFloat(newValue) || 0;
    priceTimeoutRef.current = setTimeout(() => {
      setPurchasePrice(numValue);
      if (apiData) {
        setIsCalculating(true);
        calculateProfit(numValue);
      }
    }, 1000);
  }, [apiData, calculateProfit]);

  const handleBuyBoxPriceChange = useCallback((newValue: string | number) => {
    const strValue = typeof newValue === "number" ? newValue.toString() : newValue;
    setInputBuyBoxPrice(strValue);
    if (buyBoxTimeoutRef.current) clearTimeout(buyBoxTimeoutRef.current);
    
    const numValue = parseFloat(strValue) || 0;
    buyBoxTimeoutRef.current = setTimeout(() => {
      setBuyBoxPrice(numValue);
      if (apiData) {
        setIsCalculating(true);
        calculateProfit(purchasePrice, numValue);
      }
    }, 1000);
  }, [apiData, purchasePrice, calculateProfit]);

  // Effects
  useEffect(() => {
    if (productInfo?.asin) {
      fetchEnhancedProductData(productInfo.asin);
    }
  }, [productInfo?.asin, fetchEnhancedProductData]);

  useEffect(() => {
    if (apiData && purchasePrice !== undefined && buyBoxPrice !== undefined) {
      calculateProfit(purchasePrice, buyBoxPrice);
    }
  }, [selectedCountry, apiData, purchasePrice, buyBoxPrice, calculateProfit]);


   // Handle logout
   const handleLogout = async () => {
    try {
      await logout();
    } catch (err) {
      console.error("Error during logout:", err);
      setError("Could not log out properly. Please refresh the page.");
    }
  };

  const sendMessageToParent = (type: string, data?: unknown) => {
    try {
      // First try to send to parent window (if in iframe)
      if (window.parent) {
        console.log("Sending message to parent:", type, data);
        window.parent.postMessage({ type, data }, "*");
        console.log("Message sent successfully to parent window");

        // Try Chrome extension API if available
        if (
          typeof window !== "undefined" &&
          // @ts-ignore - Access chrome in a way that TypeScript won't complain
          typeof window.chrome !== "undefined" &&
          // @ts-ignore
          typeof window.chrome.runtime !== "undefined"
        ) {
          try {
            // @ts-ignore - We've already checked existence
            window.chrome.runtime.sendMessage({ type, data }).catch(() => {
              // Silent catch - expected in non-extension environments
            });
          } catch {
            // Ignore any errors with Chrome API
          }
        }
      } else {
        console.warn("Parent window not available");
      }
    } catch (err) {
      console.error("Error sending message to parent:", err);
      // Don't display this error to user as it's not critical
    }
  };

    // Get GlobaleSearch parameters
    const getGlobalESearchParams = () => {
      // Map countries to their marketplace IDs in GlobaleSearch
      const marketplaceMap: { [key: string]: number } = {
        US: 0,
        UK: 3,
        GB: 3,
        DE: 1,
        FR: 2,
        IT: 4,
        ES: 5,
        JP: 6,
        CA: 7,
        IN: 8,
      };
  
      return marketplaceMap[selectedCountry] !== undefined
        ? marketplaceMap[selectedCountry]
        : 3; // Default to UK (3)
    };
  
    const formatWithCommas = (value: number | null | undefined): string => {
      if (value == null) return "";
      const num = Number(value);
      if (isNaN(num)) return "";
      return num.toLocaleString("en-US");
    };

    const getDetailedOffers = (): OfferItem[] => {
      try {
        if (!productInfo?.offers?.sellers_offers) {
          return [];
        }
  
        return productInfo.offers.sellers_offers.map((offer, index) => ({
          position: index + 1,
          // Cast to any to avoid type checking on this property
          type: (offer.is_amazon ? "AMZ" : offer.is_fba ? "FBA" : "FBM") as any,
          stock: offer.stock,
          price: formatToCurrency(offer.price),
        }));
      } catch (err) {
        console.error("Error getting detailed offers:", err);
        updateSectionError(
          "offers",
          "Could not process offer details. Showing limited information.",
        );
        return [];
      }
    };

    const detailedOffers = getDetailedOffers();
  
    // Handle VAT registered change
  const handleVatRegisteredChange = (isRegistered: boolean) => {
    try {
      console.log("VAT registration status changed to:", isRegistered);
      setIsVatRegistered(isRegistered);
      // No need to trigger calculation here as the calculator will do that
    } catch (err) {
      console.error("Error changing VAT registration status:", err);
      updateSectionError(
        "calculator",
        "Could not update VAT status. Please try again.",
      );
    }
  };

  const handleVatPercentageChange = (newVat: string) => {
    try {
      // Don't update if value hasn't changed
      if (newVat === vatPercentage) return;

      console.log("VAT percentage changed to:", newVat);
      setVatPercentage(newVat);

      if (apiData && purchasePrice !== undefined) {
        setIsCalculating(true);

        // Pass the new VAT value directly to ensure it's used immediately
        calculateProfit(purchasePrice, undefined, parseFloat(newVat)).catch(
          (err) => {
            console.error("Error calculating profit after VAT change:", err);
            updateSectionError(
              "calculator",
              "Could not update calculation with new VAT. Please try again.",
            );
          },
        );
      }
    } catch (err) {
      console.error("Error handling VAT percentage change:", err);
      updateSectionError("calculator", "Error updating VAT. Please try again.");
    }
  };

  const togglePriceHistory = (): void => {
    setPriceHistoryOpen(!priceHistoryOpen);
  };

  const handleLogin = async (username: string, password: string) => {
    try {
      // Clear any existing errors before attempting login
      setError(null);

      // Set the login timestamp before making the login request
      setRecentLoginTimestamp(Date.now());

      const result = await login({ username, password });

      if (result.success) {
        setIsLoginModalOpen(false);

        // After successful login, add a small delay before fetching data
        if (productInfo?.asin) {
          console.log("Login successful, will fetch enhanced data after delay");

          // Temporarily show a loading state
          setIsLoading(true);

          setTimeout(() => {
            console.log("Fetching data after login delay");
            if (productInfo && productInfo.asin) {
              fetchEnhancedProductData(productInfo.asin).catch((err) => {
                console.error("Error fetching data after login:", err);
                updateSectionError(
                  "basic",
                  "Could not refresh data after login. Please try again.",
                );
              });
            }
          }, 1000); // Slightly longer delay (1 second)
        }
      } else {
        console.log("Login failed:", result.error || "Unknown error");
      }

      return result;
    } catch (err) {
      console.error("Unexpected error during login:", err);
      return {
        success: false,
        error: "An unexpected error occurred during login. Please try again.",
      };
    }
  };

  const getAmazonDomain = () => {
    switch (selectedCountry) {
      case "US":
        return "amazon.com";
      case "UK":
      case "GB":
        return "amazon.co.uk";
      case "DE":
        return "amazon.de";
      case "FR":
        return "amazon.fr";
      case "JP":
        return "amazon.co.jp";
      case "CA":
        return "amazon.ca";
      case "CN":
        return "amazon.cn";
      case "IT":
        return "amazon.it";
      case "ES":
        return "amazon.es";
      case "IN":
        return "amazon.in";
      case "MX":
        return "amazon.com.mx";
      default:
        return "amazon.co.uk";
    }
  };


  return (
    <main className="flex flex-col h-screen max-h-screen overflow-hidden bg-gray-100">
      <div className="flex-shrink-0">
        {/* Top Row: Login/Logout controls only */}
        <div className="flex items-center justify-end bg-black px-2 py-1">
          <div className="flex items-center gap-1">
            {isAuthenticated ? (
              <div className="flex items-center">
                <div className="mr-1 flex items-center text-xs bg-gray-800 px-1 py-0.5 rounded">
                  <UserCircle size={12} className="mr-1 text-white" />
                  <span className="truncate max-w-[100px] text-white">
                    {user?.email}
                  </span>
                </div>
                <button
                  onClick={handleLogout}
                  className="text-xs hover:underline text-white"
                >
                  Logout
                </button>
              </div>
            ) : (
              <button
                onClick={() => setIsLoginModalOpen(true)}
                className="text-xs hover:underline mr-1 text-white"
              >
                Login
              </button>
            )}
            <button
              onClick={() => {
                sendMessageToParent("switchPosition");
                const button = document.activeElement as HTMLElement;
                if (button) {
                  button.classList.add("bg-gray-700");
                  setTimeout(() => button.classList.remove("bg-gray-700"), 300);
                }
              }}
              className="rounded p-0.5 text-xs text-white hover:bg-gray-700 transition-colors"
              title="Switch Side"
            >
              Switch
            </button>
            <button
              onClick={() => sendMessageToParent("toggleVisibility")}
              className="rounded-full p-0.5 transition-colors hover:bg-gray-700"
            >
              <X size={14} className="text-white" />
            </button>
          </div>
        </div>
      </div>

      {/* Content area */}
      <div
        className="flex-1 overflow-y-auto p-0"
        style={{ overflowY: "auto", WebkitOverflowScrolling: "touch" }}
      >
        <div className="bg-black">
          <header className="flex items-center justify-between w-full ps-2 py-0">
            <div className="flex-1 flex items-center">
              <h1 className="font-bold text-green-500 text-[1.6rem] tracking-wide">
                CLICKBUYDEALS
              </h1>
            </div>
            <div className="flex-1 flex items-center justify-end">
              <Image
                src="./logo.png"
                alt="ClickBuy Logo"
                height={30}
                width={160}
                className="object-contain"
              />
            </div>
          </header>
        </div>

        <div className="text-white bg-black mx-1 mt-1 px-2 py-1 grid grid-cols-12">
          <div className="col-span-11 text-center">
            <div className="flex items-center justify-center">
              <Eye size={16} className="mr-2" />
              <span className="text-sm font-medium">Watch Training Videos</span>
            </div>
          </div>
          <div className="col-span-1 flex items-center justify-end">
            <div className="border p-[0.5px] rounded-full cursor-pointer">
              <X size={13} color="white" />
            </div>
          </div>
        </div>

        <div className="bg-white flex w-full items-center justify-between text-black px-5 py-2">
          <span className="text-[11px] font-medium">
            Want To Sell More By Cross Selling?
          </span>
          <button
            onClick={() =>
              window.open("https://home.clickbuydeals.com", "_blank")
            }
            className=" text-[11px] bg-black text-white px-1 py-[2px] rounded-sm font-semibold hover:bg-gray-800 transition-colors"
          >
            List On ClickBuyDeals <span className="text-[9px]">🚀</span>
          </button>
        </div>

        <div className="relative mx-1 mt-1 bg-black text-green-500 px-2 py-1 flex items-center justify-center">
          <span className="text-sm font-extrabold tracking-wide">Overview</span>

          <div className="absolute right-2 flex items-center gap-2">
            <select
              value={selectedCountry}
              onChange={(e) => setSelectedCountry(e.target.value)}
              className="bg-gray-800 text-green-500 text-xs rounded px-1 py-0.5 border-none focus:ring-1 focus:ring-green-500"
              title="Select Country"
            >
              <option value="US">US</option>
              <option value="GB">GB</option>
            </select>

            {/* Refresh Button */}
            <button
              onClick={() => {
                if (productInfo?.asin) {
                  fetchEnhancedProductData(productInfo.asin).catch((err) => {
                    console.error("Error refreshing data:", err);
                    updateSectionError(
                      "basic",
                      "Could not refresh data. Please try again.",
                    );
                  });
                }
              }}
              className="bg-gray-800 hover:bg-gray-700 p-0.5 rounded text-green-500"
              title="Refresh Data"
            >
              <RefreshCw size={14} />
            </button>
          </div>
        </div>

        {/* Global error display */}
        {error && (
          <div
            className={`rounded mb-2 p-2 flex items-center 
        ${
          error.includes("away for a while")
            ? "bg-blue-50 text-blue-800 border border-blue-200"
            : "bg-red-50 text-red-600 border border-red-200"
        }`}
          >
            <Info className="h-4 w-4 mr-1 flex-shrink-0" />
            <p className="text-xs">{error}</p>
          </div>
        )}

        {isLoading && !productInfo ? (
          <div className="flex h-40 flex-col items-center justify-center">
            <div className="h-6 w-6 animate-spin rounded-full border-2 border-yellow-400 border-t-transparent" />
            <p className="mt-2 text-xs text-gray-500">
              Loading product information...
            </p>
            {dataLoadingProgress > 0 && (
              <div className="w-48 mt-2 bg-gray-200 rounded-full h-1.5">
                <div
                  className="bg-yellow-400 h-1.5 rounded-full"
                  style={{ width: `${dataLoadingProgress}%` }}
                ></div>
              </div>
            )}
          </div>
        ) : sectionErrors.basic && !productInfo ? (
          <div className="flex h-40 flex-col items-center justify-center space-y-1 p-2">
            <AlertCircle className="h-6 w-6 text-red-500" />
            <p className="text-center text-xs text-red-500">
              {sectionErrors.basic}
            </p>
            <button
              onClick={() => {
                sendMessageToParent("getProductData");
                updateSectionError("basic", null);
              }}
              className="rounded bg-black px-2 py-1 text-xs text-white hover:bg-gray-900"
            >
              Retry
            </button>
          </div>
        ) : productInfo ? (
          <div className="space-y-1">
            {/* Non-authenticated warning */}
            {!isAuthenticated && (
              <div className="bg-yellow-50 border border-yellow-200 rounded p-1 flex items-center space-x-1">
                <AlertTriangle size={12} className="text-yellow-500" />
                <div className="text-xs text-yellow-700 flex-1">
                  Login for enhanced data
                </div>
                <button
                  onClick={() => setIsLoginModalOpen(true)}
                  className="bg-black text-white px-1.5 py-0.5 rounded text-xs hover:bg-gray-800"
                >
                  Login
                </button>
              </div>
            )}

            {/* ROW 1: Product Information Section */}
            <div className="mx-1 border border-black overflow-hidden bg-white shadow">
              <div className="p-2">
                {sectionLoading.basic ? (
                  <div className="flex justify-center items-center h-24">
                    <div className="h-5 w-5 animate-spin rounded-full border-2 border-gray-500 border-t-transparent"></div>
                  </div>
                ) : (
                  <>
                    {/* Main content with product image and info side by side */}
                    <div className="flex gap-2 mb-2">
                      {/* Product image on left */}
                      <div className="w-1/4 h-fit border p-1 rounded-md">
                        <div className="relative aspect-square">
                          <Image
                            src={
                              productInfo.mainImage ||
                              "/api/placeholder/200/200"
                            }
                            alt={productInfo.title || "Amazon Product"}
                            width={200}
                            height={200}
                            className="object-contain w-full h-full"
                            onError={() => {
                              console.log("Error loading product image");
                            }}
                          />
                        </div>
                      </div>

                      {/* Product info on right */}
                      <div className="w-3/4">
                        {/* Product title */}
                        <div className="whitespace-normal break-words text-xs font-bold pb-1">
                          {productInfo.title || "Product Title Not Available"}
                        </div>

                        <div className="border-b border-gray-200 my-1"></div>

                        {/* Secondary info */}
                        <div className="grid grid-cols-2 gap-x-2 gap-y-0 text-[11px] mb-2">
                          <div>
                            <span className="font-medium">ASIN:</span>{" "}
                            {productInfo.asin || "N/A"}
                          </div>
                          <div>
                            <span className="font-medium">EAN:</span>{" "}
                            {apiData?.summary?.ean?.[0] || "N/A"}
                          </div>
                          <div>
                            <span className="font-medium">Brand:</span>{" "}
                            {productInfo.brand || "N/A"}
                          </div>
                          <div>
                            <span className="font-medium">Rating:</span>{" "}
                            {productInfo.rating
                              ? Number(productInfo.rating).toFixed(2)
                              : "N/A"}{" "}
                            ({productInfo.reviewCount || 0})
                          </div>
                        </div>

                        {/* Buttons for Product Details */}
                        <div className="flex flex-wrap gap-1 mt-1 mb-1">
                          <div className="relative inline-block">
                            <button className="group flex items-center bg-gray-100 px-1.5 py-0.5 rounded text-xs">
                              <Box size={10} className="mr-0.5" />
                              Dimensions
                              <div className="invisible absolute left-0 top-full mt-1 w-[220px] text-wrap rounded bg-gray-800 p-2 text-start opacity-0 shadow-lg transition-opacity duration-300 group-hover:visible group-hover:opacity-100 z-20">
                                <p className="mb-0.5 text-white text-xs">
                                  <strong>Dimensions:</strong>
                                </p>
                                <ul className="list-none text-white text-xs">
                                  <li>
                                    <strong>Height:</strong>{" "}
                                    {productInfo.dimensions?.height?.value ||
                                      "-"}{" "}
                                    {productInfo.dimensions?.height?.unit || ""}
                                  </li>
                                  <li>
                                    <strong>Length:</strong>{" "}
                                    {productInfo.dimensions?.length?.value ||
                                      "-"}{" "}
                                    {productInfo.dimensions?.length?.unit || ""}
                                  </li>
                                  <li>
                                    <strong>Width:</strong>{" "}
                                    {productInfo.dimensions?.width?.value ||
                                      "-"}{" "}
                                    {productInfo.dimensions?.width?.unit || ""}
                                  </li>
                                  <li>
                                    <strong>Weight:</strong>{" "}
                                    {productInfo.dimensions?.weight?.value ||
                                      "-"}{" "}
                                    {productInfo.dimensions?.weight?.unit || ""}
                                  </li>
                                </ul>
                              </div>
                            </button>
                          </div>

                          <div className="relative inline-block">
                            <div
                              className="flex items-center bg-gray-100 px-1.5 py-0.5 rounded text-xs cursor-pointer"
                              onClick={(e) => {
                                e.stopPropagation();
                                setIsPinned((prev) => !prev);
                              }}
                              role="button"
                              tabIndex={0}
                            >
                              <List size={10} className="mr-0.5" />
                              Features
                            </div>

                            {/* Pinned Tooltip */}
                            {isPinned && (
                              <div
                                className="fixed z-[9999] mt-1 w-[250px] text-wrap rounded bg-gray-800 text-white shadow-lg transition-opacity duration-300"
                                style={{
                                  left: "calc(50% - 125px)",
                                  transform: "translateY(30px)",
                                  pointerEvents: "auto",
                                }}
                                onClick={(e) => e.stopPropagation()}
                              >
                                <div className="flex justify-between items-center px-2 pt-2 pb-1 border-b border-gray-700">
                                  <div className="text-xs font-semibold">
                                    Features:
                                  </div>
                                  <span
                                    className="text-xs cursor-pointer hover:text-gray-300"
                                    onClick={() => setIsPinned(false)}
                                  >
                                    ✕
                                  </span>
                                </div>

                                <div className="max-h-[200px] overflow-y-auto px-2 py-1">
                                  {productInfo.features &&
                                  productInfo.features.length > 0 ? (
                                    <ul className="list-disc pl-4 space-y-1">
                                      {productInfo.features.map(
                                        (feature, index) => (
                                          <li key={index} className="text-xs">
                                            {feature}
                                          </li>
                                        ),
                                      )}
                                    </ul>
                                  ) : (
                                    <p className="text-xs text-gray-300">
                                      No feature information available
                                    </p>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>

                          {/* Links Popover */}
                          <div className="relative inline-block">
                            <button
                              onClick={() => setIsPinned(!isPinned)}
                              className="flex items-center bg-gray-100 px-1.5 py-0.5 rounded text-xs"
                            >
                              <Box size={10} className="mr-0.5" />
                              Links
                            </button>
                            {isPinned && (
                              <div className="absolute left-0 top-full mt-1 w-[220px] text-wrap rounded bg-gray-800 p-2 text-start shadow-lg z-20">
                                <div className="flex flex-col gap-1">
                                  {/* eBay Live */}
                                  {apiData?.summary &&
                                  (apiData.summary as any)
                                    ?.ebay_active_listing_url ? (
                                    <a
                                      href={
                                        (apiData?.summary as any)
                                          ?.ebay_active_listing_url
                                      }
                                      target="_blank"
                                      rel="noopener noreferrer"
                                    >
                                      <button className="w-full bg-blue-100 text-blue-800 hover:bg-blue-200 px-1.5 py-0.5 rounded text-xs font-medium">
                                        eBay Live
                                      </button>
                                    </a>
                                  ) : (
                                    <button
                                      disabled
                                      className="w-full bg-gray-100 text-gray-400 px-1.5 py-0.5 rounded text-xs font-medium"
                                    >
                                      eBay Live
                                    </button>
                                  )}

                                  {/* eBay Sold */}
                                  {apiData?.summary &&
                                  (apiData.summary as any)
                                    ?.ebay_sold_listing_url ? (
                                    <a
                                      href={
                                        (apiData?.summary as any)
                                          ?.ebay_sold_listing_url
                                      }
                                      target="_blank"
                                      rel="noopener noreferrer"
                                    >
                                      <button className="w-full bg-green-100 text-green-800 hover:bg-green-200 px-1.5 py-0.5 rounded text-xs font-medium">
                                        eBay Sold
                                      </button>
                                    </a>
                                  ) : (
                                    <button
                                      disabled
                                      className="w-full bg-gray-100 text-gray-400 px-1.5 py-0.5 rounded text-xs font-medium"
                                    >
                                      eBay Sold
                                    </button>
                                  )}

                                  {/* Hagglezon */}
                                  {apiData?.summary &&
                                  (apiData.summary as any)?.hagglezon_url ? (
                                    <a
                                      href={
                                        (apiData?.summary as any)?.hagglezon_url
                                      }
                                      target="_blank"
                                      rel="noopener noreferrer"
                                    >
                                      <button className="w-full bg-yellow-100 text-yellow-800 hover:bg-yellow-200 px-1.5 py-0.5 rounded text-xs font-medium">
                                        Hagglezon
                                      </button>
                                    </a>
                                  ) : (
                                    <button
                                      disabled
                                      className="w-full bg-gray-100 text-gray-400 px-1.5 py-0.5 rounded text-xs font-medium"
                                    >
                                      Hagglezon
                                    </button>
                                  )}

                                  {/* Global */}
                                  <a
                                    href={`https://globalesearch.com/?searchTerm=${apiData?.summary?.ean?.[0] || productInfo.asin}&lt=1&lt=2&sortBy=price&category=-1&searchInDescription=false&se=0&se=${getGlobalESearchParams()}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                  >
                                    <button className="w-full bg-purple-100 text-purple-800 hover:bg-purple-200 px-1.5 py-0.5 rounded text-xs font-medium">
                                      Global
                                    </button>
                                  </a>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                )}

                {/* Key metrics grid */}
                <div className="grid grid-cols-5 gap-1 mb-1">
                  {/* Purchase Price */}
                  <div className="border border-black rounded p-1 flex flex-col items-center bg-gray-100">
                    <div className="font-bold text-[10px]">Buy Price</div>
                    <div className="relative w-full mt-2">
                      {isAuthenticated ? (
                        <div className="flex items-center border border-gray-400 rounded focus:ring-1 focus:ring-black focus:border-black gap-1 overflow-hidden">
                          <span className=" text-xs inset-y-0 left-0 flex items-center pl-1 text-gray-500">
                            {getCurrencySymbol(selectedCountry)}
                          </span>
                          <input
                            type="text"
                            value={inputPurchasePrice}
                            onChange={(e) => {
                              const value = e.target.value.replace(
                                /[^0-9.]/g,
                                "",
                              );
                              const parts = value.split(".");
                              const formattedValue =
                                parts.length > 2
                                  ? parts[0] + "." + parts.slice(1).join("")
                                  : value;
                              handlePurchasePriceChange(formattedValue);
                            }}
                            className="w-full py-0 text-xs font-bold text-center"
                          />
                          {isCalculating && (
                            <span className="absolute inset-y-0 right-1 flex items-center">
                              <div className="h-3 w-3 animate-spin rounded-full border-2 border-black border-t-transparent"></div>
                            </span>
                          )}
                        </div>
                      ) : (
                        <div className="group relative w-full">
                          <button
                            onClick={() => setIsLoginModalOpen(true)}
                            className="w-full py-0.5 text-center border rounded bg-gray-100 flex items-center justify-center"
                          >
                            <Lock size={14} className="text-gray-500" />
                          </button>
                          <div className="invisible absolute top-0 right-0 translate-x-full ml-1 px-2 py-1 text-xs bg-gray-800 text-white rounded opacity-0 transition-opacity group-hover:visible group-hover:opacity-100 z-50 whitespace-nowrap">
                            Login to use calculator
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Sale Price */}
                  <div className="border border-black rounded p-1 flex flex-col items-center bg-gray-100">
                    <div className="font-bold text-[10px]">Sells price</div>
                    <div className="relative w-full mt-2">
                      {isAuthenticated ? (
                        <div className="flex items-center border border-gray-400 rounded focus:ring-1 focus:ring-black focus:border-black gap-1 overflow-hidden">
                          <span className="text-xs inset-y-0 left-0 flex items-center pl-1 text-gray-500">
                            {getCurrencySymbol(selectedCountry)}
                          </span>
                          <input
                            type="text"
                            value={inputBuyBoxPrice}
                            onChange={(e) => {
                              const value = e.target.value.replace(
                                /[^0-9.]/g,
                                "",
                              );
                              const parts = value.split(".");
                              const formattedValue =
                                parts.length > 2
                                  ? parts[0] + "." + parts.slice(1).join("")
                                  : value;
                              handleBuyBoxPriceChange(formattedValue);
                            }}
                            className="w-full py-0 text-xs font-bold text-center"
                          />
                          {isCalculating && (
                            <span className="absolute inset-y-0 right-1 flex items-center">
                              <div className="h-3 w-3 animate-spin rounded-full border-2 border-black border-t-transparent"></div>
                            </span>
                          )}
                        </div>
                      ) : (
                        <div className="group relative w-full">
                          <button
                            onClick={() => setIsLoginModalOpen(true)}
                            className="w-full py-0.5 text-center border rounded bg-gray-100 flex items-center justify-center"
                          >
                            <Lock size={14} className="text-gray-500" />
                          </button>
                          <div className="invisible absolute top-0 right-0 translate-x-full ml-1 px-2 py-1 text-xs bg-gray-800 text-white rounded opacity-0 transition-opacity group-hover:visible group-hover:opacity-100 z-50 whitespace-nowrap">
                            Login to use calculator
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* BSR */}
                  <div className="border border-black rounded p-1 flex flex-col items-center bg-gray-100 justify-between">
                    <div className="font-bold text-[10px]">BSR</div>
                    <div className="text-xs font-bold mb-[2px]">
                      {formatWithCommas(
                        typeof productInfo?.bsr === "string"
                          ? parseFloat(productInfo?.bsr)
                          : productInfo?.bsr,
                      ) ||
                        formatWithCommas(
                          productInfo?.pricing?.non_vat_pricing?.sales_rank,
                        ) ||
                        "N/A"}
                    </div>
                  </div>

                  {/* ROI */}
                  <div className="border border-black rounded p-1 flex flex-col items-center bg-gray-100 justify-between">
                    <div className="font-bold text-[10px] flex items-center">
                      <TrendingUp size={10} className="mr-0.5" />
                      ROI
                    </div>
                    <div
                      className={`text-xs font-bold mb-[2px] ${(profitData?.roi || 0) > 0 ? "text-green-600" : "text-red-600"}`}
                    >
                      {typeof profitData?.roi === "number"
                        ? profitData.roi.toFixed(1)
                        : "0.0"}
                      %
                    </div>
                  </div>

                  {/* Profit */}
                  <div className="border border-black rounded p-1 flex flex-col items-center bg-gray-100 justify-between">
                    <div className="font-bold text-[10px] flex items-center">
                      Profit
                    </div>
                    <div
                      className={`text-xs font-bold mb-[2px] ${(profitData?.profit || 0) > 0 ? "text-green-600" : "text-red-600"}`}
                    >
                      {formatToCurrency(profitData?.profit || 0)}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* AI Summarizer Section */}
            <AISummarizer
              estimatedSales={productInfo?.estimated_sales || 0}
              profit={profitData?.profit || 0}
              roi={profitData?.roi || 0}
              isGated={gatedStatus?.restrictions?.length > 0}
              bsr={
                productInfo?.bsr ||
                productInfo?.pricing?.non_vat_pricing?.sales_rank ||
                "0"
              }
              stockLevel={
                productInfo?.offers?.sellers_offers?.[0]?.stock || "0"
              }
              amazonInBuyBox={productInfo?.warnings?.amz_in_buy_box || false}
              isAuthenticated={isAuthenticated}
              buyBoxHistory={apiData?.buy_box_history || {}}
              onLoginClick={() => setIsLoginModalOpen(true)}
            />

            {/* ROW 2: Warnings Section - Dummy Design */}
            <div className=" overflow-hidden bg-white shadow mx-1">
              {/* <div className="bg-black text-green-500 px-2 py-1 flex items-center justify-center">
        <span className="text-sm font-bold">WARNINGS & RESTRICTIONS</span>
        <button 
          onClick={() => setWarningsOpen(!warningsOpen)}
          className="text-green-500 hover:text-green-400 absolute right-3"
        >
          <svg 
            className={`w-4 h-4 transition-transform ${warningsOpen ? 'rotate-180' : ''}`} 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
      </div> */}

              {warningsOpen && (
                <div className="p-2 border  border-black">
                  <div className="grid grid-cols-3 gap-2">
                    {/* Amazon in Buy Box */}
                    <div className="bg-gray-100 border border-gray-400 rounded py-[0.5px]  px-1 flex items-center justify-between">
                      <span className="text-[9.5px] font-bold whitespace-nowrap">
                        Amazon in Buy Box:
                      </span>
                      <span
                        className={`text-[9.5px] font-bold ${
                          productInfo.warnings?.amz_in_buy_box
                            ? "text-red-600"
                            : "text-green-600"
                        }`}
                      >
                        {productInfo.warnings?.amz_in_buy_box ? "Yes" : "No"}
                      </span>
                    </div>

                    {/* Adult Product */}
                    <div className="bg-gray-100 border border-gray-400 rounded py-[0.5px]  px-1 flex items-center justify-between">
                      <span className="text-[9.5px] font-bold whitespace-nowrap">
                        Adult Product:
                      </span>
                      <span
                        className={`text-[9.5px] font-bold ${
                          productInfo.warnings?.adult_product
                            ? "text-red-600"
                            : "text-green-600"
                        }`}
                      >
                        {productInfo.warnings?.adult_product ? "Yes" : "No"}
                      </span>
                    </div>

                    {/* Meltable Product */}
                    <div className="bg-gray-100 border border-gray-400 rounded py-[0.5px]  px-1 flex items-center justify-between">
                      <span className="text-[9.5px] font-bold whitespace-nowrap">
                        Meltable Product:
                      </span>
                      <span
                        className={`text-[9.5px] font-bold ${
                          productInfo.warnings?.meltable
                            ? "text-red-600"
                            : "text-green-600"
                        }`}
                      >
                        {productInfo.warnings?.meltable ? "Yes" : "No"}
                      </span>
                    </div>

                    {/* Has Variations */}
                    <div className="bg-gray-100 border border-gray-400 rounded py-[0.5px]  px-1 flex items-center justify-between">
                      <span className="text-[9.5px] font-bold whitespace-nowrap">
                        Has Variations:
                      </span>
                      <span
                        className={`text-[9.5px] font-bold ${
                          productInfo.warnings?.variations?.is_variations &&
                          productInfo.warnings?.variations?.variations_value
                            ?.length > 0
                            ? "text-red-600"
                            : "text-green-600"
                        }`}
                      >
                        {productInfo.warnings?.variations?.is_variations &&
                        productInfo.warnings?.variations?.variations_value
                          ?.length > 0
                          ? "Yes"
                          : "No"}
                      </span>
                    </div>

                    {/* Category Gated */}
                    <div className="bg-gray-100 border border-gray-400 rounded py-[0.5px]  px-1 flex items-center justify-between">
                      <span className="text-[9.5px] font-bold whitespace-nowrap">
                        Category Gated:
                      </span>
                      <span
                        className={`text-[9.5px] font-bold ${
                          gatedStatus?.restrictions?.length > 0
                            ? "text-red-600"
                            : "text-green-600"
                        }`}
                      >
                        {gatedStatus?.restrictions?.length > 0 ? "Yes" : "No"}
                      </span>
                    </div>

                    {/* Private Label */}
                    <div className="bg-gray-100 border border-gray-400 rounded py-[0.5px]  px-1 flex items-center justify-between">
                      <span className="text-[9.5px] font-bold whitespace-nowrap">
                        Private Label:
                      </span>
                      <span className="text-[9.5px] font-bold text-green-600">
                        No
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* ROW 3: Offers Section - Dummy Design */}
            <div className="bg-white border border-black mx-1">
              <div className="bg-black text-green-500 px-2 py-1 flex items-center justify-center">
                <span className="text-sm font-bold">OFFERS</span>
              </div>
              <div className="p-2">
                {sectionLoading.offers ? (
                  <div className="flex justify-center items-center h-24">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-500 border-t-transparent"></div>
                  </div>
                ) : (
                  <>
                    {/* Total and breakdown */}
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-xs font-bold">
                        Total: {productInfo.offers?.total || 0}
                      </span>
                      <div className="flex gap-2 text-xs">
                        <span>
                          <span className="text-green-500 text-md">●</span> FBA:{" "}
                          {productInfo.offers?.fba || 0}
                        </span>
                        <span>
                          <span className="text-yellow-500 text-md">●</span>{" "}
                          FBM: {productInfo.offers?.fbm || 0}
                        </span>
                      </div>
                    </div>

                    {/* Offers table */}
                    <table className="w-full text-xs border rounded-lg">
                      <thead>
                        <tr className="bg-gray-100">
                          <th className="p-1 text-center border">#</th>
                          <th className="p-1 text-center border">Seller</th>
                          <th className="p-1 text-center border">Stock</th>
                          <th className="p-1 text-center border">Price</th>
                        </tr>
                      </thead>
                      <tbody>
                        {detailedOffers.length > 0 ? (
                          <>
                            {detailedOffers
                              .slice(
                                0,
                                isOffersExpanded ? detailedOffers.length : 5,
                              )
                              .map((offer) => (
                                <tr key={offer.position}>
                                  <td className="p-1 text-center border">
                                    {offer.position}
                                  </td>
                                  <td className="p-1 text-center border">
                                    <span
                                      className={`px-1 rounded ${
                                        (offer.type as any) === "AMZ"
                                          ? "bg-purple-100 text-purple-800"
                                          : offer.type === "FBA"
                                            ? "bg-green-100 text-green-800"
                                            : "bg-yellow-100 text-yellow-800"
                                      }`}
                                    >
                                      {offer.type}
                                    </span>
                                  </td>
                                  <td className="p-1 text-center border">
                                    {offer.stock}
                                  </td>
                                  <td className="p-1 text-center border">
                                    {offer.price}
                                  </td>
                                </tr>
                              ))}
                          </>
                        ) : (
                          <tr>
                            <td
                              colSpan={4}
                              className="p-1 text-center text-gray-500 text-xs border"
                            >
                              {isAuthenticated
                                ? "No offers data available"
                                : "Login to view offers"}
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>

                    {/* Show All button */}
                    {detailedOffers.length > 5 && (
                      <div className="text-center mt-2">
                        <button
                          onClick={() => setIsOffersExpanded(!isOffersExpanded)}
                          className="text-xs text-blue-600 hover:underline"
                        >
                          {isOffersExpanded
                            ? "Show Less"
                            : `Show All (${detailedOffers.length})`}
                        </button>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>

            {/* ROW 4: Calculator and Graphs */}
            <div className=" overflow-hidden bg-white shadow m-1">
              <div className="py-0">
                {/* Enhanced Calculator */}
                {calculatorOpen && (
                  <div
                    className={sectionLoading.calculator ? "opacity-50" : ""}
                  >
                    <CompactProfitCalculator
                      isVatRegistered={isVatRegistered}
                      onVatRegisteredChange={handleVatRegisteredChange}
                      asin={productInfo.asin || ""}
                      initialData={{
                        buy_box_price:
                          productInfo.pricing?.non_vat_pricing?.buy_box_price ||
                          0,
                        fba_fees:
                          productInfo.pricing?.non_vat_pricing?.fba_fee || 0,
                        referral_percent:
                          apiData?.pricing?.referral_fee
                            ?.referral_fee_percentage || 15,
                        sales_rank:
                          typeof productInfo.pricing?.non_vat_pricing
                            ?.sales_rank === "number" &&
                          !isNaN(
                            productInfo.pricing?.non_vat_pricing?.sales_rank,
                          )
                            ? productInfo.pricing.non_vat_pricing.sales_rank
                            : 0,
                      }}
                      isAuthenticated={isAuthenticated}
                      country={selectedCountry}
                      onProfitCalculated={(data) => {
                        const dataId = JSON.stringify({
                          profit:
                            data.not_vat_registed?.fees?.profit ||
                            data.vat_registed?.fees?.profit,
                          roi:
                            data.not_vat_registed?.fees?.roi ||
                            data.vat_registed?.fees?.roi,
                          purchase_price: data.purchase_price,
                          buy_box_price: data.buy_box_price,
                          vat_status: data.currentVatStatus,
                          country: selectedCountry,
                        });

                        if (dataId === lastProfitDataId) {
                          console.log("Skipping duplicate profit data update");
                          setIsCalculating(false);
                          return;
                        }

                        setLastProfitDataId(dataId);

                        const feesData = data.currentVatStatus
                          ? data.vat_registed?.fees
                          : data.not_vat_registed?.fees;

                        if (feesData) {
                          const profitData = {
                            not_vat_registed: data.not_vat_registed,
                            vat_registed: data.vat_registed,
                            buy_box_price: data.metrics.buybox_price,
                            fba_fee: data.metrics.fba_fees,
                            profit: feesData.profit,
                            referral_fee: data.metrics.referral_fee,
                            roi: feesData.roi,
                            total_fee: feesData.total_fees,
                            variable_closing_fee:
                              data.metrics.variable_closing_fee || 0,
                            purchase_price:
                              data.purchase_price || purchasePrice,
                            country: selectedCountry,
                          };

                          setProfitData(profitData);
                          updateSectionError("calculator", null);
                        } else {
                          console.warn(
                            "Missing fees data in calculator response",
                          );
                          updateSectionError(
                            "calculator",
                            "Some calculator data is missing. Results may be incomplete.",
                          );
                        }

                        setIsCalculating(false);
                      }}
                      purchasePrice={purchasePrice}
                      onPurchasePriceChange={(price: number) =>
                        handlePurchasePriceChange(price.toString())
                      }
                      buyBoxPrice={buyBoxPrice}
                      onBuyBoxPriceChange={handleBuyBoxPriceChange}
                      vatPercentage={vatPercentage}
                      onVatPercentageChange={handleVatPercentageChange}
                    />
                  </div>
                )}

                {/* Price History Section */}
                <div className="mt-1">
                  <PriceHistorySection
                    isAuthenticated={isAuthenticated}
                    productInfo={productInfo}
                    onLoginClick={() => setIsLoginModalOpen(true)}
                    isError={!!sectionErrors.priceHistory}
                    country={selectedCountry}
                    collapsed={!priceHistoryOpen}
                    onToggleCollapse={togglePriceHistory}
                  />
                </div>

                {/* Average Price History */}
                <div className="mt-2">
                  <AverageDataSection
                    rankAndPriceHistory={apiData?.rank_and_price_history}
                    isAuthenticated={isAuthenticated}
                    onLoginClick={() => setIsLoginModalOpen(true)}
                    country={selectedCountry}
                  />
                </div>

                {/* Buy Box History Section */}
                <div className="mt-2">
                  <BuyBoxHistorySection
                    isAuthenticated={isAuthenticated}
                    buyBoxHistory={apiData?.buy_box_history}
                    onLoginClick={() => setIsLoginModalOpen(true)}
                    country={selectedCountry}
                  />
                </div>
              </div>
            </div>

            {/* ClickbuyAds Section */}
            <ClickbuyAds
              collapsed={!clickbuyAdsOpen}
              onToggleCollapse={toggleClickbuyAds}
            />
          </div>
        ) : (
          <div className="flex h-40 flex-col items-center justify-center p-2 text-gray-500">
            <div className="h-5 w-5 animate-spin rounded-full border-2 border-yellow-400 border-t-transparent" />
            <p className="text-center text-xs mt-2">
              Loading product information...
            </p>
            {dataLoadingProgress > 0 && (
              <div className="w-48 mt-2 bg-gray-200 rounded-full h-1.5">
                <div
                  className="bg-yellow-400 h-1.5 rounded-full"
                  style={{ width: `${dataLoadingProgress}%` }}
                ></div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Login Modal */}
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
        onLogin={handleLogin}
        isLoading={authLoading}
        error={authError}
      />

      {showSettings && <SettingsPanel onClose={() => setShowSettings(false)} />}

      {isGatingTooltipOpen && (
        <div
          className="fixed inset-0 z-[10000]"
          onClick={() => setIsGatingTooltipOpen(false)}
        >
          <div
            className="absolute bg-[#1f2937] rounded-md shadow-lg p-2 w-[220px]"
            style={{
              top: `${tooltipPosition.y}px`,
              left: `${tooltipPosition.x}px`,
              transform: "translate(0, -100%)",
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div
              className="absolute w-3 h-3 bg-[#1f2937] transform rotate-45"
              style={{
                bottom: "-6px",
                right: "10px",
              }}
            />

            <div className="flex justify-between items-center mb-1">
              <span className="text-xs font-semibold text-white">
                Gating Restriction:{" "}
                {gatedStatus?.restrictions?.[0]?.reasons?.[0]?.message ||
                  "Approval required"}
              </span>
              <span
                className="text-white text-xs cursor-pointer hover:text-gray-300"
                onClick={() => setIsGatingTooltipOpen(false)}
              >
                ✕
              </span>
            </div>
            <button
              className="w-full bg-[#dc2626] text-white text-xs py-1 px-2 rounded"
              onClick={() => {
                try {
                  const amazonDomain = getAmazonDomain();
                  const sellerCentralDomain = amazonDomain.replace(
                    "amazon",
                    "sellercentral.amazon",
                  );

                  const approvalLink =
                    gatedStatus?.restrictions?.[0]?.reasons?.[0]?.links?.[0]
                      ?.resource ||
                    `https://${sellerCentralDomain}/hz/approvalrequest/restrictions/approve?asin=${productInfo?.asin}`;
                  window.open(approvalLink, "_blank");
                } catch (err) {
                  console.error("Error opening approval link:", err);
                  updateSectionError(
                    "gated",
                    "Could not open approval request. Please try again.",
                  );
                }
              }}
            >
              Request Approval
            </button>
          </div>
        </div>
      )}

      {showChatBot && (
        <ChatBot
          isOpen={showChatBot}
          onClose={() => setShowChatBot(false)}
          productInfo={{
            title: productInfo?.title,
            asin: productInfo?.asin,
            bsr:
              productInfo?.bsr ||
              productInfo?.pricing?.non_vat_pricing?.sales_rank,
            estimated_sales: productInfo?.estimated_sales,
          }}
          profitData={{
            profit: profitData?.profit,
            roi: profitData?.roi,
          }}
        />
      )}
    </main>
  );
}
